<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>折扣系统逻辑可视化</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10.6.1/dist/mermaid.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 40px;
        }
        .section h2 {
            color: #2196F3;
            border-bottom: 2px solid #2196F3;
            padding-bottom: 10px;
        }
        .mermaid {
            text-align: center;
            margin: 20px 0;
        }
        .legend {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .legend h3 {
            margin-top: 0;
            color: #495057;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        .legend-color {
            width: 20px;
            height: 20px;
            margin-right: 10px;
            border-radius: 3px;
        }
        .key-features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .feature-card {
            background: #fff;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .feature-card h4 {
            color: #495057;
            margin-top: 0;
        }
        .code-snippet {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛍️ 商品折扣叠加系统 - 逻辑可视化</h1>
        
        <div class="section">
            <h2>📊 系统整体架构流程</h2>
            <div class="mermaid">
                graph TD
                    A[页面加载] --> B{DOM是否就绪?}
                    B -->|是| C[初始化折扣系统]
                    B -->|否| D[等待DOMContentLoaded]
                    D --> C
                    
                    C --> E[获取全局折扣配置]
                    E --> F[处理现有商品]
                    F --> G[启动MutationObserver]
                    
                    G --> H[监听DOM变化]
                    H --> I{检测到变化?}
                    I -->|商品卡片| J[处理商品折扣]
                    I -->|价格容器| J
                    I -->|徽标元素| J
                    I -->|其他| H
                    
                    J --> K[解析商品信息]
                    K --> L{缓存中有数据?}
                    L -->|有且价格未变| M[复用缓存数据]
                    L -->|无或价格变化| N[重新计算折扣]
                    
                    M --> O[更新DOM显示]
                    N --> P[调用API获取折扣]
                    P --> Q[计算最终价格]
                    Q --> R[缓存结果]
                    R --> O
                    
                    O --> S[批量更新同款商品]
                    S --> H
                    
                    style A fill:#e1f5fe
                    style C fill:#f3e5f5
                    style J fill:#fff3e0
                    style O fill:#e8f5e8
            </div>
        </div>

        <div class="section">
            <h2>🔄 商品处理核心逻辑</h2>
            <div class="mermaid">
                graph LR
                    A[商品卡片] --> B[解析DOM信息]
                    B --> C{获取商品ID}
                    C -->|成功| D[检测上下文类型]
                    C -->|失败| E[跳过处理]
                    
                    D --> F{卡片类型?}
                    F -->|普通卡片| G[标准处理流程]
                    F -->|advc-product-item| H[ADVC专用处理]
                    F -->|recommend-product-item| I[推荐商品处理]
                    
                    G --> J[计算折扣价格]
                    H --> K[就地更新价格区]
                    I --> L[推荐商品价格更新]
                    
                    J --> M[更新徽标和价格]
                    K --> N[处理隐藏元素]
                    L --> O[处理节省显示]
                    
                    M --> P[标记已更新]
                    N --> P
                    O --> P
                    
                    style A fill:#e3f2fd
                    style D fill:#fff3e0
                    style J fill:#f1f8e9
                    style P fill:#fce4ec
            </div>
        </div>

        <div class="section">
            <h2>💰 折扣计算逻辑</h2>
            <div class="mermaid">
                graph TD
                    A[当前售价] --> B[获取API折扣数据]
                    B --> C{有可用折扣?}
                    C -->|否| D[保持原价格]
                    C -->|是| E[遍历折扣梯度]
                    
                    E --> F{价格满足门槛?}
                    F -->|否| G[检查下一梯度]
                    F -->|是| H[应用折扣比例]
                    
                    G --> I{还有梯度?}
                    I -->|是| F
                    I -->|否| D
                    
                    H --> J[计算最终售价]
                    J --> K[计算总折扣率]
                    K --> L[返回价格数据]
                    
                    D --> M[返回原始数据]
                    
                    style A fill:#e8f5e8
                    style H fill:#fff3e0
                    style J fill:#f3e5f5
                    style L fill:#e1f5fe
            </div>
        </div>

        <div class="legend">
            <h3>🎨 图例说明</h3>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #e1f5fe;"></div>
                <span>系统初始化阶段</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fff3e0;"></div>
                <span>数据处理阶段</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #e8f5e8;"></div>
                <span>计算逻辑阶段</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background-color: #fce4ec;"></div>
                <span>DOM更新阶段</span>
            </div>
        </div>

        <div class="section">
            <h2>🔧 核心功能特性</h2>
            <div class="key-features">
                <div class="feature-card">
                    <h4>🎯 智能缓存机制</h4>
                    <p>使用 Map 缓存商品折扣数据，避免重复API调用。支持价格变化检测和缓存失效。</p>
                    <div class="code-snippet">productInfoMap.set(productId, {...})</div>
                </div>
                
                <div class="feature-card">
                    <h4>🔄 实时DOM监听</h4>
                    <p>通过 MutationObserver 监听页面变化，自动处理动态加载的商品卡片。</p>
                    <div class="code-snippet">observer.observe(document.body, config)</div>
                </div>
                
                <div class="feature-card">
                    <h4>📱 多类型卡片支持</h4>
                    <p>支持普通商品卡片、推荐商品、ADVC商品等多种DOM结构。</p>
                    <div class="code-snippet">advc-product-item | recommend-product-item</div>
                </div>
                
                <div class="feature-card">
                    <h4>💎 折扣叠加算法</h4>
                    <p>在当前售价基础上叠加API折扣，支持多梯度折扣规则。</p>
                    <div class="code-snippet">finalPrice = salePrice * (1 - discount%)</div>
                </div>
                
                <div class="feature-card">
                    <h4>🎨 动态UI更新</h4>
                    <p>自动创建和更新价格元素、折扣徽标，保持页面样式一致性。</p>
                    <div class="code-snippet">updateProductCardDisplay()</div>
                </div>
                
                <div class="feature-card">
                    <h4>🔍 上下文感知</h4>
                    <p>区分商品详情页和列表页上下文，应用不同的样式和布局。</p>
                    <div class="code-snippet">context: 'detail' | 'card'</div>
                </div>
            </div>
        </div>

        <div class="section">
            <h2>📋 关键数据流</h2>
            <div class="mermaid">
                sequenceDiagram
                    participant U as 用户
                    participant D as DOM
                    participant S as 折扣系统
                    participant A as API
                    participant C as 缓存
                    
                    U->>D: 访问页面
                    D->>S: 触发初始化
                    S->>A: 获取折扣配置
                    A-->>S: 返回折扣数据
                    S->>D: 扫描现有商品
                    
                    loop 监听变化
                        D->>S: DOM变化事件
                        S->>C: 检查缓存
                        alt 缓存命中
                            C-->>S: 返回缓存数据
                        else 缓存未命中
                            S->>A: 请求商品折扣
                            A-->>S: 返回折扣信息
                            S->>C: 更新缓存
                        end
                        S->>S: 计算最终价格
                        S->>D: 更新商品显示
                    end
            </div>
        </div>
    </div>

    <script>
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true
            },
            sequence: {
                useMaxWidth: true
            }
        });
    </script>
</body>
</html>
